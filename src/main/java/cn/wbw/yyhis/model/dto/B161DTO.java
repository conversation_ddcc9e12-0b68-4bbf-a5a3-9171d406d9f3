package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B161 DTO - 常规检查记录
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B161DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 单次就诊唯一标识号 */
    private String visitSn;

    /** 报告单号 */
    private String reportNo;

    /** 检查项目类型 */
    private String examItemType;

    /** 检查部位 */
    private String examSites;

    /** 记录创建时间 */
    private String recordDatetime;

    /** 检查时间 */
    private String examDatetime;

    /** 申请时间 */
    private String applyDatetime;

    /** 检查项目名称 */
    private String examItemName;

    /** 检查诊断结论 */
    private String examDiagConclusion;

    /** 检查诊断描述 */
    private String examDiagDescription;

}