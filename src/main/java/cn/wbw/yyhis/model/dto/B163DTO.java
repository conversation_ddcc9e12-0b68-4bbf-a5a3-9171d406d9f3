package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B163 DTO - 分子病理检测记录
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B163DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告单号
     */
    private String reportNo;

    /**
     * 报告时间
     */
    private String reportDatetime;

    /**
     * 申请时间
     */
    private String applyDatetime;

    /**
     * 检测方法
     */
    private String testMethod;

    /**
     * 检测结果
     */
    private String testResult;

}