package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B071 DTO - 出院记录
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B071DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 入院情况 */
    private String admissionCondition;

    /** 入院时间 */
    private String admissionDatetime;

    /** 入院诊断 */
    private String admissionDiag;

    /** 文书内容传递格式 */
    private String contentType;

    /** 出院情况 */
    private String dischargeCondition;

    /** 出院时间 */
    private String dischargeDatetime;

    /** 出院诊断 */
    private String dischargeDiag;

    /** 出院医嘱 */
    private String dischargeOrder;

    /** 出院原因 */
    private String dischargeReason;

    /** ECOGPS评分 */
    private String ecogScore;

    /** 扩展字段1 */
    private String extendData1;

    /** 扩展字段2 */
    private String extendData2;

    /** 来源表 */
    private String fromTable;

    /** 来源数据唯一标识 */
    private String fromYyRecordId;

    /** 组织机构代码 */
    private String hospitalCode;

    /** 医疗机构名称 */
    private String hospitalName;

    /** 住院次数 */
    private String hospitalizationTimes;

    /** 住院号 */
    private String inpatientNo;

    /** KPS评分 */
    private String kpsScore;

    /** 实际住院天数 */
    private String lengthOfStay;

    /** 病案号 */
    private String medicalRecordNo;

    /** 患者ID */
    private String patientId;

    /** 患者原始ID */
    private String patientIdOld;

    /** 记录创建时间 */
    private LocalDateTime recordDatetime;

    /** 出院记录流水号 */
    private String recordSn;

    /** 记录状态 */
    private Integer recordStatus;

    /** 文本模板名称 */
    private String recordTemplateName;

    /** 文书内容 */
    private String recordText;

    /** 文档标题 */
    private String recordTitle;

    /** 记录更新时间 */
    private LocalDateTime recordUpdateDatetime;

    /** 医生签名 */
    private String signatureDoctor;

    /** 诊疗经过 */
    private String treatmentInfo;

    /** 单次就诊唯一标识号 */
    private String visitSn;

    /** 数据采集时间 */
    private LocalDateTime yyCollectionDatetime;

    /** 数据唯一标识 */
    private Long yyRecordId;

    /** 数据行唯一记录 */
    private String yyRecordMd5;

    /** 上报状态 */
    private Integer yyUploadStatus;

    /** 抽取完成时间 */
    private LocalDateTime yyEtlTime;

    /** 上报完成时间 */
    private LocalDateTime yyUploadTime;

    /** 上报批次号 */
    private String yyBatchTime;

    /** 数据插入唯一号 */
    private String yyRecordBatchId;

    /** 数据反填时间 */
    private LocalDateTime yyBackfillTime;

    /** 数据反填状态 */
    private Integer yyBackfillStatus;

    /** 分院编码 */
    private String branchCode;

    /** 分院名称 */
    private String branchName;

    /** 分区用业务日期 */
    private LocalDateTime dateForPartition;
}