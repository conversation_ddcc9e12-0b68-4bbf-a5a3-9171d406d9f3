package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B101 DTO - 住院医嘱记录
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B101DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 给药途径 */
    private String administrationRoute;

    /** 抗菌药物标识 */
    private String antibioticsLevel;

    /** 单次剂量 */
    private String dose;

    /** 单次剂量单位 */
    private String doseUnit;

    /** 用药配伍 */
    private String drugCompatibility;

    /** 是否药品 */
    private String drugFlag;

    /** 用药顺序 */
    private String drugOrder;

    /** 医嘱执行科室代码 */
    private String executiveDeptCode;

    /** 医嘱执行科室名称 */
    private String executiveDeptName;

    /** 扩展字段1 */
    private String extendData1;

    /** 扩展字段2 */
    private String extendData2;

    /** 药品剂型 */
    private String form;

    /** 使用频率代码 */
    private String frequencyCode;

    /** 使用频率 */
    private String frequencyName;

    /** 来源表 */
    private String fromTable;

    /** 来源数据唯一标识 */
    private String fromYyRecordId;

    /** 草药备注 */
    private String herbalNote;

    /** 组织机构代码 */
    private String hospitalCode;

    /** 医疗机构名称 */
    private String hospitalName;

    /** 住院次数 */
    private String hospitalizationTimes;

    /** 输液时长 */
    private String infusionDuration;

    /** 注射方式 */
    private String injectionType;

    /** 住院号 */
    private String inpatientNo;

    /** 总金额(元) */
    private String itemTotalPrice;

    /** 单价(元) */
    private String itemUnitPrice;

    /** 职务名称 */
    private String jobTitle;

    /** 液体配置 */
    private String liquidConfiguration;

    /** 生产厂家 */
    private String manufac;

    /** 病案号 */
    private String medicalRecordNo;

    /** 医嘱项目数量 */
    private String numOfOrder;

    /** 医嘱项目数量单位 */
    private String numOfOrderUnit;

    /** 手术记录流水号 */
    private String operationId;

    /** 医嘱分类代码 */
    private String orderClassCode;

    /** 医嘱分类名称 */
    private String orderClassName;

    /** 医嘱确认时间 */
    private String orderConfirmTime;

    /** 医嘱下达科室代码 */
    private String orderDeptCode;

    /** 医嘱下达科室名称 */
    private String orderDeptName;

    /** 医嘱开单医生名称 */
    private String orderDoctorName;

    /** 医嘱开单医生代码 */
    private String orderDoctorNo;

    /** 医嘱停止时间 */
    private String orderEndDatetime;

    /** 医嘱开具时间 */
    private String orderGivenTime;

    /** 医嘱组号 */
    private String orderGroupNo;

    /** 医嘱项目代码 */
    private String orderItemCode;

    /** 医嘱项目名称 */
    private String orderItemName;

    /** 医嘱备注 */
    private String orderNote;

    /** 医嘱流水号 */
    private String orderSn;

    /** 医嘱开始时间 */
    private String orderStartDatetime;

    /** 医嘱状态 */
    private String orderStatus;

    /** 医嘱类别 */
    private String orderType;

    /** 患者ID */
    private String patientId;

    /** 患者原始ID */
    private String patientIdOld;

    /** 记录创建时间 */
    private String recordDatetime;

    /** 记录状态 */
    private Integer recordStatus;

    /** 记录更新时间 */
    private LocalDateTime recordUpdateDatetime;

    /** 申请单号 */
    private String requestNo;

    /** 药品规格 */
    private String spec;

    /** 特殊医嘱标识 */
    private String specialOrderFlag;

    /** 技术职称 */
    private String technicalTitle;

    /** 药品商品名 */
    private String tradeName;

    /** 就诊科室代码 */
    private String visitDeptCode;

    /** 就诊科室名称 */
    private String visitDeptName;

    /** 单次就诊唯一标识号 */
    private String visitSn;

    /** 数据采集时间 */
    private LocalDateTime yyCollectionDatetime;

    /** 数据唯一标识 */
    private Long yyRecordId;

    /** 数据行唯一记录 */
    private String yyRecordMd5;

    /** 上报状态 */
    private Integer yyUploadStatus;

    /** 抽取完成时间 */
    private LocalDateTime yyEtlTime;

    /** 上报完成时间 */
    private LocalDateTime yyUploadTime;

    /** 上报批次号 */
    private String yyBatchTime;

    /** 数据插入唯一号 */
    private String yyRecordBatchId;

    /** 数据反填时间 */
    private LocalDateTime yyBackfillTime;

    /** 数据反填状态 */
    private Integer yyBackfillStatus;

    /** 分院编码 */
    private String branchCode;

    /** 分院名称 */
    private String branchName;

    /** 分区用业务日期 */
    private LocalDateTime dateForPartition;
}