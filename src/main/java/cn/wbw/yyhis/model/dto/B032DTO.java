package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B032 DTO - 住院患者信息表(在院)
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B032DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入院床位编码
     */
    private String admissionBedCode;

    /**
     * 入院床位名称
     */
    private String admissionBedName;

    /**
     * 入院时间
     */
    private LocalDateTime admissionDatetime;

    /**
     * 入院科室代码
     */
    private String admissionDeptCode;

    /**
     * 入院科室名称
     */
    private String admissionDeptName;

    /**
     * 入院医疗组代码
     */
    private String admissionMedicalTeamCode;

    /**
     * 入院医疗组名称
     */
    private String admissionMedicalTeamName;

    /**
     * 入院方式代码
     */
    private String admissionTypeCode;

    /**
     * 入院方式名称
     */
    private String admissionTypeName;

    /**
     * 入院病区代码
     */
    private String admissionWardCode;

    /**
     * 入院病区名称
     */
    private String admissionWardName;

    /**
     * 主治医师名称
     */
    private String attendingPhysician;

    /**
     * 主治医师代码
     */
    private String attendingPhysicianId;

    /**
     * 主（副主）任医师
     */
    private String chiefPhysician;

    /**
     * 主（副主）任医师编码
     */
    private String chiefPhysicianId;

    /**
     * 当前床位编码
     */
    private String currentBedCode;

    /**
     * 当前床位名称
     */
    private String currentBedName;

    /**
     * 当前科室编码
     */
    private String currentDeptCode;

    /**
     * 当前科室名称
     */
    private String currentDeptName;

    /**
     * 当前病区编码
     */
    private String currentWardCode;

    /**
     * 当前病区名称
     */
    private String currentWardName;

    /**
     * 扩展字段1
     */
    private String extendData1;

    /**
     * 扩展字段2
     */
    private String extendData2;

    /**
     * 来源表
     */
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    private String fromYyRecordId;

    /**
     * 组织机构代码
     */
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    private String hospitalName;

    /**
     * 住院次数
     */
    private String hospitalizationTimes;

    /**
     * 住院号
     */
    private String inpatientNo;

    /**
     * 病案号
     */
    private String medicalRecordNo;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 患者原始ID
     */
    private String patientIdOld;

    /**
     * 记录创建时间
     */
    private LocalDateTime recordDatetime;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    private LocalDateTime recordUpdateDatetime;

    /**
     * 责任护士
     */
    private String responsibleNurse;

    /**
     * 责任护士编码
     */
    private String responsibleNurseId;

    /**
     * 就诊卡号
     */
    private String visitCardNo;

    /**
     * 就诊医生姓名
     */
    private String visitDoctorName;

    /**
     * 就诊医生代码
     */
    private String visitDoctorNo;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 数据采集时间
     */
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    private String branchCode;

    /**
     * 分院名称
     */
    private String branchName;

    /**
     * 分区用业务日期
     */
    private LocalDateTime dateForPartition;
}