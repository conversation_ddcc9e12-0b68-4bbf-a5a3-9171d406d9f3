package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B051 DTO - 入院记录
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B051DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 辅助检查
     */
    private String auxiliaryExam;

    /**
     * 体表面积
     */
    private String bodySurfaceArea;

    /**
     * 体温
     */
    private String bodyTemperature;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 确诊诊断
     */
    private String confirmedDiagnosis;

    /**
     * 文书内容传递格式
     */
    private String contentType;

    /**
     * 现病史
     */
    private String currentMedhistory;

    /**
     * 舒张压
     */
    private String diastolicPressure;

    /**
     * ECOGPS评分
     */
    private String ecogScore;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 来源表
     */
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    private String fromYyRecordId;

    /**
     * 心率
     */
    private String heartRate;

    /**
     * 身高
     */
    private String height;

    /**
     * 组织机构代码
     */
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    private String hospitalName;

    /**
     * 住院次数
     */
    private String hospitalizationTimes;

    /**
     * 住院号
     */
    private String inpatientNo;

    /**
     * KPS评分
     */
    private String kpsScore;

    /**
     * 婚育史
     */
    private String marriageBirthHistory;

    /**
     * 病案号
     */
    private String medicalRecordNo;

    /**
     * 月经史
     */
    private String menstrualHistory;

    /**
     * 其他诊断
     */
    private String otherDiagnosis;

    /**
     * 既往史
     */
    private String pastMedhistory;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 患者原始ID
     */
    private String patientIdOld;

    /**
     * 个人史
     */
    private String personalMedhistory;

    /**
     * 体格检查
     */
    private String physicalExam;

    /**
     * 初步诊断
     */
    private String primaryDiagnosis;

    /**
     * 记录创建时间
     */
    private LocalDateTime recordDatetime;

    /**
     * 入院记录流水号
     */
    private String recordSn;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * 文本模板名称
     */
    private String recordTemplateName;

    /**
     * 文书内容
     */
    private String recordText;

    /**
     * 文档标题
     */
    private String recordTitle;

    /**
     * 记录更新时间
     */
    private LocalDateTime recordUpdateDatetime;

    /**
     * 呼吸
     */
    private String respiratoryRate;

    /**
     * 修订诊断
     */
    private String revisionDiagnosis;

    /**
     * 医生签名
     */
    private String signatureDoctor;

    /**
     * 专科检查
     */
    private String specialExam;

    /**
     * 收缩压
     */
    private String systolicPressure;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 体重
     */
    private String weight;

    /**
     * 数据采集时间
     */
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    private String branchCode;

    /**
     * 分院名称
     */
    private String branchName;

    /**
     * 分区用业务日期
     */
    private LocalDateTime dateForPartition;
}