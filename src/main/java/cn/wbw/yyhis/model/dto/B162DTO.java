package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B162 DTO - 病理检查记录
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B162DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 报告单号 */
    private String reportNo;

    /** 病理检查类型 */
    private String pathologyTestType;

    /** 报告时间 */
    private String reportDatetime;

    /** 申请时间 */
    private String applyDatetime;

    /** 大体描述 */
    private String grossDescription;

    /** 病理诊断结论 */
    private String pathoDiagConclusion;
}