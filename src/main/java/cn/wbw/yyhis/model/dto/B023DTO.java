package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B023 DTO - 患者诊断记录
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B023DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否明确诊断
     */
    private String confirmedDiagMark;

    /**
     * 诊断科室编码
     */
    private String createDiagnoseDeptCode;

    /**
     * 诊断科室名称
     */
    private String createDiagnoseDeptName;

    /**
     * 诊断编码
     */
    private String diagCode;

    /**
     * 诊断时间
     */
    private String diagDatetime;

    /**
     * 诊断医生名称
     */
    private String diagDoctorName;

    /**
     * 诊断医生代码
     */
    private String diagDoctorNo;

    /**
     * 诊断说明
     */
    private String diagExplanation;

    /**
     * 诊断ID号
     */
    private String diagId;

    /**
     * 诊断名称
     */
    private String diagName;

    /**
     * 诊断序号
     */
    private String diagSerialNumber;

    /**
     * 诊断数据来源
     */
    private String diagSource;

    /**
     * 诊断状态
     */
    private String diagStatus;

    /**
     * 诊断类型
     */
    private String diagType;

    /**
     * 扩展字段1
     */
    private String extendData1;

    /**
     * 扩展字段2
     */
    private String extendData2;

    /**
     * 来源表
     */
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    private String fromYyRecordId;

    /**
     * 组织机构代码
     */
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    private String hospitalName;

    /**
     * 住院次数
     */
    private String hospitalizationTimes;

    /**
     * 住院号
     */
    private String inpatientNo;

    /**
     * 是否并发
     */
    private String isComplication;

    /**
     * 是否主要诊断
     */
    private String maindiagMark;

    /**
     * 病案号
     */
    private String medicalRecordNo;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 门诊号
     */
    private String outpatientNo;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 患者原始ID
     */
    private String patientIdOld;

    /**
     * 记录创建时间
     */
    private String recordDatetime;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    private LocalDateTime recordUpdateDatetime;

    /**
     * 中医诊断的病编码
     */
    private String tcmDiseaseCode;

    /**
     * 中医诊断的病名称
     */
    private String tcmDiseaseName;

    /**
     * 中医诊断的证编码
     */
    private String tcmSymptomCode;

    /**
     * 中医诊断的证名称
     */
    private String tcmSymptomName;

    /**
     * 就诊卡号
     */
    private String visitCardNo;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 就诊次数
     */
    private String visitTimes;

    /**
     * 就诊类型
     */
    private String visitType;

    /**
     * 数据采集时间
     */
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    private String branchCode;

    /**
     * 分院名称
     */
    private String branchName;

    /**
     * 分区用业务日期
     */
    private LocalDateTime dateForPartition;
}