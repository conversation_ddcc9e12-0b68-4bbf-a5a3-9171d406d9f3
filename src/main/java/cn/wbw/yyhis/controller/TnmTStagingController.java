package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.TnmTStagingConverter;
import cn.wbw.yyhis.model.dto.TnmTStagingDTO;
import cn.wbw.yyhis.model.dto.TnmTStagingUpsertDTO;
import cn.wbw.yyhis.model.entity.TnmTStaging;
import cn.wbw.yyhis.service.TnmTStagingService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TNM分期记录管理接口
 *
 * 提供肿瘤TNM分期记录的管理功能，包括肿瘤分期信息的记录和查询。
 * TNM分期是国际通用的肿瘤分期系统，用于描述肿瘤的发展程度，
 * 包括原发肿瘤大小（T）、淋巴结转移情况（N）、远处转移情况（M），
 * 为肿瘤治疗方案制定和预后评估提供重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@RestController
@RequestMapping("/tnm-t-staging")
@RequiredArgsConstructor
public class TnmTStagingController {

    private final TnmTStagingService tnmTStagingService;
    private final TnmTStagingConverter converter = TnmTStagingConverter.INSTANCE;

    /**
     * 保存TNM分期记录
     *
     * 新增肿瘤TNM分期记录，记录患者肿瘤的分期信息。
     * TNM分期是肿瘤诊疗的重要环节，为治疗方案制定和预后评估提供依据。
     *
     * @param dto TNM分期记录数据传输对象，包含以下主要字段：
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - diagnosisItemCode: 临床诊断编码，关联的肿瘤诊断
     *            - tnmTStaging: 肿瘤分期-T，描述原发肿瘤的大小和侵犯程度
     *            - tnmNStaging: 肿瘤分期-N，描述区域淋巴结转移情况
     *            - tnmMStaging: 肿瘤分期-M，描述远处转移情况
     *            - figoStagingCervicalCancer: FIGO分期-宫颈癌，妇科肿瘤专用分期
     *            - cnlcStagingHepatocellular: CNLC分期-肝细胞癌，肝癌专用分期
     *            - annArborStagingLymphoma: Ann Arbor分期-淋巴瘤，淋巴瘤专用分期
     *            - stagingDatetime: 分期时间，进行分期评估的时间
     * @return TnmTStagingDTO 保存后的TNM分期记录完整信息
     */
    @PostMapping("/save")
    public TnmTStagingDTO save(@RequestBody TnmTStagingUpsertDTO dto) {
        TnmTStaging newRecord = tnmTStagingService.addTnmTStagingRecord(dto);
        return converter.entityToDto(newRecord);
    }

    /**
     * 根据就诊流水号删除TNM分期记录
     *
     * 删除指定就诊的TNM分期记录。删除分期记录需要谨慎操作，
     * 因为分期信息是肿瘤治疗的重要依据，建议在删除前确认
     * 该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return tnmTStagingService.deleteByVisitSn(visitSn);
    }

    /**
     * 查询所有TNM分期记录
     *
     * 获取系统中的所有TNM分期记录，用于统计分析或管理查看。
     * 该接口返回完整的分期记录列表，包含所有患者的肿瘤分期信息。
     *
     * @return List<TnmTStagingDTO> 所有TNM分期记录列表，包含：
     *         - recordSn: TNM分期记录流水号
     *         - visitSn: 就诊流水号
     *         - diagnosisItemCode: 临床诊断编码
     *         - tnmTStaging: T分期
     *         - tnmNStaging: N分期
     *         - tnmMStaging: M分期
     *         - stagingDatetime: 分期时间
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<TnmTStagingDTO> findAll() {
        return converter.entityListToDtoList(tnmTStagingService.list());
    }

    /**
     * 根据就诊流水号查询TNM分期记录详情
     *
     * 查询指定就诊的TNM分期记录详细信息，包含完整的肿瘤分期数据。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return TnmTStagingDTO TNM分期记录详细信息，包含：
     *         - recordSn: TNM分期记录流水号
     *         - visitSn: 就诊流水号
     *         - diagnosisItemCode: 临床诊断编码
     *         - tnmTStaging: 肿瘤分期-T（原发肿瘤）
     *         - tnmNStaging: 肿瘤分期-N（淋巴结）
     *         - tnmMStaging: 肿瘤分期-M（远处转移）
     *         - figoStagingCervicalCancer: FIGO分期-宫颈癌
     *         - cnlcStagingHepatocellular: CNLC分期-肝细胞癌
     *         - annArborStagingLymphoma: Ann Arbor分期-淋巴瘤
     *         - stagingDatetime: 分期时间
     *         - recordDatetime: 记录创建时间
     *         - recordUpdateDatetime: 记录更新时间
     *         如果该就诊没有TNM分期记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public TnmTStagingDTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(tnmTStagingService.lambdaQuery().eq(TnmTStaging::getVisitSn, visitSn).one());
    }

    /**
     * 分页查询TNM分期记录
     *
     * 分页获取TNM分期记录列表，支持大数据量的分期记录查询和展示。
     * 适用于肿瘤分期记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return Page<TnmTStagingDTO> 分页的TNM分期记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的TNM分期记录列表，每条记录包含完整的肿瘤分期信息
     */
    @GetMapping("/page")
    public Page<TnmTStagingDTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TnmTStaging> page = new Page<>(pageNum, pageSize);
        Page<TnmTStaging> entityPage = tnmTStagingService.page(page);
        Page<TnmTStagingDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
}
