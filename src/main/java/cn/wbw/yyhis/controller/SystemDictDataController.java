package cn.wbw.yyhis.controller;

import cn.hutool.core.util.StrUtil;
import cn.wbw.yyhis.converter.SystemDictDataConverter;
import cn.wbw.yyhis.model.dto.SystemDictDataDTO;
import cn.wbw.yyhis.model.entity.SystemDictData;
import cn.wbw.yyhis.service.SystemDictDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 系统字典数据管理接口
 *
 * 提供系统字典数据的管理功能，包括字典数据的增删改查、导入等操作。
 * 字典数据是系统的基础配置信息，为医疗信息系统提供标准化的数据选项，
 * 如疾病编码（ICD-10）、药品分类、检查项目等，确保数据的标准化和一致性。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/system-dict-data")
@RequiredArgsConstructor
public class SystemDictDataController {

    private final SystemDictDataService systemDictDataService;
    private final SystemDictDataConverter converter = SystemDictDataConverter.INSTANCE;

    /**
     * 导入字典数据
     *
     * 导入标准的字典数据，如ICD-10疾病编码等标准化数据。
     * 该接口用于系统初始化或更新标准字典数据，
     * 确保系统使用最新的标准化编码体系。
     *
     * @return Boolean 导入操作结果，true表示导入成功，false表示导入失败
     * @throws IOException 文件读取异常
     */
    @PostMapping("/import")
    public Boolean importData() throws IOException {
        systemDictDataService.importIcd10Data();
        return true;
    }

    /**
     * 新增字典数据
     *
     * 添加新的字典数据项，用于扩展系统的字典配置。
     * 新增的字典数据将用于系统的下拉选项、数据验证等功能。
     *
     * @param dto 字典数据传输对象，包含以下主要字段：
     *            - label: 字典标签，显示给用户的文本
     *            - value: 字典值，系统内部使用的值
     *            - dictType: 字典类型，用于分类管理字典数据
     *            - sort: 排序号，控制字典项的显示顺序
     *            - status: 状态，控制字典项是否启用
     *            - colorType: 颜色类型，用于前端显示样式
     *            - remark: 备注信息
     * @return Boolean 新增操作结果，true表示新增成功，false表示新增失败
     */
    @PostMapping("/add")
    public Boolean add(@RequestBody SystemDictDataDTO dto) {
        return systemDictDataService.save(converter.dtoToEntity(dto));
    }

    /**
     * 根据ID删除字典数据
     *
     * 删除指定的字典数据项。删除字典数据需要谨慎操作，
     * 确保该字典项没有被系统其他功能引用，
     * 避免影响系统的正常运行。
     *
     * @param id 字典数据ID，唯一标识一条字典数据
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable Long id) {
        return systemDictDataService.removeById(id);
    }

    /**
     * 根据ID更新字典数据
     *
     * 更新指定的字典数据项信息，可以修改字典标签、值、状态等字段。
     * 更新字典数据时需要注意保持数据的一致性和完整性。
     *
     * @param id 字典数据ID，指定要更新的字典数据
     * @param dto 字典数据传输对象，包含更新的字段信息
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update/{id}")
    public Boolean update(@PathVariable Long id, @RequestBody SystemDictDataDTO dto) {
        dto.setId(id);
        return systemDictDataService.updateById(converter.dtoToEntity(dto));
    }

    /**
     * 查询所有字典数据
     *
     * 获取系统中的所有字典数据，用于系统管理或数据导出。
     * 该接口返回完整的字典数据列表，包含所有字典类型的数据。
     *
     * @return List<SystemDictDataDTO> 所有字典数据列表，包含：
     *         - id: 字典数据ID
     *         - label: 字典标签
     *         - value: 字典值
     *         - dictType: 字典类型
     *         - sort: 排序号
     *         - status: 状态
     *         - createTime: 创建时间
     *         - updateTime: 更新时间
     */
    @GetMapping("/all")
    public List<SystemDictDataDTO> findAll() {
        return converter.entityListToDtoList(systemDictDataService.list());
    }

    /**
     * 根据ID查询字典数据详情
     *
     * 查询指定字典数据的详细信息，用于数据查看或编辑准备。
     *
     * @param id 字典数据ID，唯一标识一条字典数据
     * @return SystemDictDataDTO 字典数据详细信息，包含：
     *         - id: 字典数据ID
     *         - label: 字典标签
     *         - value: 字典值
     *         - dictType: 字典类型
     *         - sort: 排序号
     *         - status: 状态
     *         - colorType: 颜色类型
     *         - cssClass: CSS样式类
     *         - remark: 备注信息
     *         - creator: 创建人
     *         - createTime: 创建时间
     *         - updater: 更新人
     *         - updateTime: 更新时间
     *         如果指定ID的字典数据不存在，返回null
     */
    @GetMapping("/detail/{id}")
    public SystemDictDataDTO findById(@PathVariable Long id) {
        return converter.entityToDto(systemDictDataService.getById(id));
    }

    /**
     * 分页查询字典数据
     *
     * 分页获取指定字典类型的字典数据列表，支持按标签模糊查询。
     * 适用于字典数据的管理界面，提供高效的数据查询和展示功能。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @param dictType 字典类型，必填参数，用于筛选特定类型的字典数据
     * @param label 字典标签，可选参数，用于模糊查询字典标签
     * @return Page<SystemDictDataDTO> 分页的字典数据列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的字典数据列表，每条记录包含完整的字典信息
     */
    @GetMapping("/page")
    public Page<SystemDictDataDTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam String  dictType,
                                            @RequestParam(required = false) String label) {
        Page<SystemDictData> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SystemDictData> wrapper = new QueryWrapper<SystemDictData>().lambda()
                .eq(SystemDictData::getDictType, dictType)
                .like(StrUtil.isNotBlank(label),SystemDictData::getLabel, label);
        Page<SystemDictData> entityPage = systemDictDataService.page(page,wrapper);
        Page<SystemDictDataDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }

    /**
     * 根据字典类型查询字典数据列表
     *
     * 查询指定字典类型的所有字典数据，用于前端下拉选项、数据验证等功能。
     * 返回的数据按排序号排序，确保前端显示的顺序正确。
     *
     * @param dictType 字典类型，如：disease_code（疾病编码）、drug_type（药品类型）、
     *                 exam_type（检查类型）等，用于获取特定分类的字典数据
     * @return List<SystemDictDataDTO> 指定类型的字典数据列表，包含：
     *         - label: 字典标签，用于前端显示
     *         - value: 字典值，用于数据存储和传输
     *         - sort: 排序号，控制显示顺序
     *         - status: 状态，只返回启用状态的数据
     *         如果指定类型的字典数据不存在，返回空列表
     */
    @GetMapping("/{dictType}")
    public List<SystemDictDataDTO> getByDictType(@PathVariable String dictType) {
        List<SystemDictData> list = systemDictDataService.lambdaQuery().eq(SystemDictData::getDictType, dictType).list();
        return converter.entityListToDtoList(list);
    }
} 